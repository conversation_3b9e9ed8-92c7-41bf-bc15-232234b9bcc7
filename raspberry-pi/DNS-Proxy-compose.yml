# This stack configures Pihole for DNS resolution and includes Cloudflared for DNS-over-HTTPS.
services:
  pihole:
    # Pihole provides ad-blocking and DNS capabilities.
    container_name: pihole
    hostname: pihole
    image: pihole/pihole:latest
    ports:
      - "53:53/tcp"      # Expose DNS service on TCP
      - "53:53/udp"      # Expose DNS service on UDP
      - "8085:80/tcp"    # Expose Pihole web interface on port 8085 (trying to leave port 80 for nginx-proxy-manager)
    environment:
      TZ: 'America/Denver'       # Timezone configuration
      WEBPASSWORD: 'K94EF4CU'    # Admin password for Pihole UI
    volumes:
      - 'pihole_pihole_data:/etc/pihole'        # Persistent storage for Pihole data
      - 'pihole_dnsmasq_data:/etc/dnsmasq.d'    # Persistent storage for DNS configuration
    networks:
      - pihole-network  # Shared network to allow communication with other services
    restart: unless-stopped

    
  cloudflared:
    # Cloudflared runs as a DNS-over-HTTPS proxy
    image: cloudflare/cloudflared:latest
    container_name: cloudflared
    command: >
      proxy-dns --port 5053
      --upstream https://*******/dns-query
      --upstream https://*******/dns-query
    restart: unless-stopped
    networks:
      - pihole-network  # Shared network to allow communication with Pihole


  nginx-proxy-manager:
  # Reverse Proxy - NGINX to manage access to the stack services
    image: 'jc21/nginx-proxy-manager:latest'
    restart: unless-stopped
    container_name: nginx-proxy-manager
    ports:
      - '80:80'
      - '81:81'
      - '443:443'
    volumes:
      - monitoring_data:/data                       # using volumes from monitoring stack
      - monitoring_letsencrypt:/etc/letsencrypt     # for proxy 

volumes:
  pihole_pihole_data:
    external: true  # Use existing external volume
  pihole_dnsmasq_data:
    external: true  # Use existing external volume
  monitoring_data:  #for nginx-proxy-manager
    external: true  # Use existing external volume
  monitoring_letsencrypt:  #for nginx-proxy manager
    external: true  # Use existing external volume

networks:
  pihole-network:
    name: pihole-network  # Ensure network consistency between stacks



