#!/bin/bash

# Homebox Automated Backup Script for macOS
# This script creates automated backups of the Homebox Docker container
# Author: Generated for <PERSON>'s homelab setup
# Date: $(date +%Y-%m-%d)

# Configuration
BACKUP_DIR="$HOME/docker-backups"
CONTAINER_NAME="homebox"
VOLUME_NAME="homebox-data"
LOG_FILE="$BACKUP_DIR/backup.log"
BACKUP_RETENTION_DAYS=30  # Keep backups for 30 days
DATE_FORMAT=$(date +%Y%m%d_%H%M%S)
BACKUP_FILENAME="homebox_backup_${DATE_FORMAT}.tar"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to log messages
log_message() {
    local level=$1
    local message=$2
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo -e "${timestamp} [${level}] ${message}" | tee -a "$LOG_FILE"
}

# Function to send notification (macOS)
send_notification() {
    local title=$1
    local message=$2
    local sound=$3
    osascript -e "display notification \"$message\" with title \"$title\" sound name \"$sound\""
}

# Function to check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_message "ERROR" "Docker is not running or not accessible"
        send_notification "Backup Failed" "Docker is not running" "Basso"
        exit 1
    fi
}

# Function to check if container exists and is running
check_container() {
    if ! docker ps --format "table {{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_message "ERROR" "Container '${CONTAINER_NAME}' is not running"
        send_notification "Backup Failed" "Homebox container is not running" "Basso"
        exit 1
    fi
    log_message "INFO" "Container '${CONTAINER_NAME}' is running"
}

# Function to create backup directory
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_message "INFO" "Created backup directory: $BACKUP_DIR"
    fi
}

# Function to perform the backup
perform_backup() {
    log_message "INFO" "Starting backup of Homebox data..."
    
    # Run the backup command
    if docker run --rm --volumes-from "$CONTAINER_NAME" -v "$BACKUP_DIR:/backup" ubuntu tar cvf "/backup/$BACKUP_FILENAME" /data >/dev/null 2>&1; then
        # Check if backup file was created and has content
        if [ -f "$BACKUP_DIR/$BACKUP_FILENAME" ] && [ -s "$BACKUP_DIR/$BACKUP_FILENAME" ]; then
            local backup_size=$(du -h "$BACKUP_DIR/$BACKUP_FILENAME" | cut -f1)
            log_message "SUCCESS" "Backup completed successfully: $BACKUP_FILENAME (Size: $backup_size)"
            send_notification "Backup Successful" "Homebox backup completed ($backup_size)" "Glass"
            return 0
        else
            log_message "ERROR" "Backup file was not created or is empty"
            send_notification "Backup Failed" "Backup file was not created properly" "Basso"
            return 1
        fi
    else
        log_message "ERROR" "Docker backup command failed"
        send_notification "Backup Failed" "Docker backup command failed" "Basso"
        return 1
    fi
}

# Function to clean up old backups
cleanup_old_backups() {
    log_message "INFO" "Cleaning up backups older than $BACKUP_RETENTION_DAYS days..."
    
    local deleted_count=0
    # Find and delete backup files older than retention period
    while IFS= read -r -d '' file; do
        rm "$file"
        deleted_count=$((deleted_count + 1))
        log_message "INFO" "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "homebox_backup_*.tar" -type f -mtime +$BACKUP_RETENTION_DAYS -print0 2>/dev/null)
    
    if [ $deleted_count -gt 0 ]; then
        log_message "INFO" "Cleaned up $deleted_count old backup(s)"
    else
        log_message "INFO" "No old backups to clean up"
    fi
}

# Function to display backup statistics
show_backup_stats() {
    local backup_count=$(find "$BACKUP_DIR" -name "homebox_backup_*.tar" -type f | wc -l)
    local total_size=$(du -sh "$BACKUP_DIR" 2>/dev/null | cut -f1)
    log_message "INFO" "Backup statistics: $backup_count backup(s), Total size: $total_size"
}

# Main execution
main() {
    log_message "INFO" "=== Homebox Backup Script Started ==="
    
    # Pre-flight checks
    check_docker
    check_container
    create_backup_dir
    
    # Perform backup
    if perform_backup; then
        cleanup_old_backups
        show_backup_stats
        log_message "INFO" "=== Backup process completed successfully ==="
        exit 0
    else
        log_message "ERROR" "=== Backup process failed ==="
        exit 1
    fi
}

# Handle script interruption
trap 'log_message "WARNING" "Backup script interrupted"; exit 130' INT TERM

# Run main function
main "$@"
