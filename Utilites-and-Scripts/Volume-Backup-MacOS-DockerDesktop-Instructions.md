# Step-by-Step Guide

**Note:** These instructions have been tested with Homebox deployed via Portainer on Docker Desktop for macOS. If you're using a different setup, some adjustments may be required. These instructions assume you did not change any info found in the deployment documentation for Homebox found here:<https://homebox.software/en/installation.html#docker-compose>

## 1. Prerequisites

- **Docker Desktop:** Ensure Docker Desktop is installed and running on your Mac Mini.
- **Terminal Access:** You’ll be using the Terminal for these commands.

---

### 2. Prepare a Local Backup Directory

Create a folder on your Mac Mini where your backups will be saved. Open your Terminal and run:

`mkdir -p ~/docker-backups`

This command creates a folder named `docker-backups` in your home directory.

- So in my lab it would create a folder `/Users/<USER>/docker-backups/`

---

## 3. Identify the Container and Volume to Back Up

Your `homebox` container uses a volume named `homebox-data` mounted to `/data`. This is the data you want to back up.

---

## 4. Create a Backup Container Using Docker Run

Based on Docker Docs: <https://docs.docker.com/engine/storage/volumes/#back-up-restore-or-migrate-data-volumes>

You can use a temporary container (based on the `ubuntu` image) to back up the data. This container will:

- **Access the Volume:** Use `--volumes-from homebox` to access the `/data` volume.
- **Mount the Local Backup Folder:** Use `-v ~/docker-backups:/backup` to map your local folder to `/backup` inside the container.
- **Run the Tar Command:** Archive the `/data` folder into a tar file stored in `/backup`.

Run the following command in your Terminal:

`docker run --rm --volumes-from homebox -v ~/docker-backups:/backup ubuntu tar cvf /backup/homebox_backup.tar /data`

### **Explanation:**

- `docker run --rm`: Starts a container and automatically removes it after the command finishes.
- `--volumes-from homebox`: Mounts all volumes from the running `homebox` container into the new container.
- `-v ~/docker-backups:/backup`: Mounts your local `~/docker-backups` folder to `/backup` inside the container.
- `ubuntu`: Uses the Ubuntu image (which includes the `tar` utility).
- `tar cvf /backup/homebox_backup.tar /data`: Creates a tar archive of the `/data` folder (your volume) and saves it as `homebox_backup.tar` in `/backup`.

---

## 5. Verify Your Backup

After the command finishes, navigate to your local backup folder to verify the backup file was created:

`open ~/docker-backups`

You should see a file named `homebox_backup.tar` in the folder.

---

## 6. (Optional) Step-by-Step Instructions to Restore the Backup

### 1. Make Sure Your Backup File is Accessible

Your backup file (homebox_backup.tar) needs to be in a folder on your computer called "docker-backups" in your home directory. This assumes you created a new container instance and volume of Homebox.

If it's not there already:

- Open Terminal
- Type: `mkdir -p ~/docker-backups`
- Copy your backup file into this folder
- Ensure its called homebox_backup.tar

### 2. Run the Magic Restore Command

Copy and paste this command into Terminal:


`docker run --rm --volumes-from homebox -v ~/docker-backups:/backup ubuntu bash -c "cd /data && tar xvf /backup/homebox_backup.tar --strip 1"`

What this command does:

1. **Creates a temporary container based on Ubuntu**
    - `docker run ubuntu`: This starts a brand new container using the Ubuntu operating system image
    - This container is completely separate from your Homebox container, but we'll use it as a tool
2. **Gives this container access to your Homebox's storage**
    - `--volumes-from homebox`: This is like giving the temporary container a "key" to access the same storage that your Homebox container uses
    - Even though the two containers are separate, this temporary container can now see and modify the files in Homebox's volume
3. **Connects your computer's docker-backups folder to this container**
    - `-v ~/docker-backups:/backup`: This creates a "bridge" between a folder on your computer and a folder inside the container
    - The container can now see your backup file through this bridge
    - In technical terms, your computer's ~/docker-backups folder is "mounted" at /backup inside the container
4. **Tells the container to unpack your backup file into Homebox's storage**
    - `bash -c "cd /data && tar xvf /backup/homebox_backup.tar --strip 1"`: This runs a command inside the container to:
        - Go to the /data directory (where Homebox's storage is mounted)
        - Use the "tar" program to extract (x) with verbose output (v) from a file (f)
        - The file it extracts is your backup file, which it can see at /backup/homebox_backup.tar
        - The `--strip 1` option removes the first folder level from the archive, preventing nested directories
5. **Automatically removes the temporary container when finished**
    - `--rm`: This flag tells Docker to delete the temporary container once its job is done
    - This keeps your system clean - the container was just a tool to transfer your backup data

When this command completes, your backup data will be extracted directly into Homebox's storage volume, making it immediately available to your Homebox container.

### 3. Restart Your Homebox Container

After the restore finishes:

`docker restart homebox`

This tells Homebox to reload and use your restored data.

### 4. Check If It Worked

Open your web browser and go to:

- [http://localhost:3100](http://localhost:3100)

You should now see your Homebox container with all your restored data!
