name: twingate-connector
services:
    connector:
        environment:
            - TWINGATE_NETWORK=mortigiamon
            - TWINGATE_ACCESS_TOKEN=get-from-page
            - TWINGATE_REFRESH_TOKEN=get-from-page
            - TWINGATE_LABEL_HOSTNAME=`hostname`
            - TWINGATE_LOG_ANALYTICS=v2
            - TWINGATE_LABEL_DEPLOYED_BY=docker
        container_name: twingate-discreet-donkey-dennis
        network_mode: host
        restart: unless-stopped
        pull_policy: always
        image: twingate/connector:1
