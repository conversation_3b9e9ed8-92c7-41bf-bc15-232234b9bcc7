services:
  hbbs:
    container_name: hbbs
    image: rustdesk/rustdesk-server:latest
    command: hbbs

    volumes:
      - hbbs_data:/root
    ports:
      - "21114:21114"  # TCP
      - "21115:21115"  # TCP
      - "21116:21116"  # TCP
      - "21116:21116/UDP"  # UDP
      - "21120:21118"  # TCP
    restart: unless-stopped

  hbbr:
    container_name: hbbr
    image: rustdesk/rustdesk-server:latest
    command: hbbr
    volumes:
      - hbbr_data:/root
    ports:
      - "21119:21119"  # TCP 
      - "21117:21117"  # TCP
    restart: unless-stopped

volumes:
  hbbs_data:
  hbbr_data:
