name: open-webui
services:
    open-webui:
        ports:
            - 11024:8080
        deploy:
            resources:
                reservations:
                    devices:
                        - driver: nvidia
                          count: all
                          capabilities:
                              - gpu
        extra_hosts:
            - host.docker.internal:host-gateway
        volumes:
            - open-webui:/app/backend/data
        container_name: open-webui
        restart: always
        image: ghcr.io/open-webui/open-webui:cuda
volumes:
    open-webui:
        name: open-webui
