services:
  twingate_connector:
    container_name: twingate-homeconnector
    restart: always
    image: "twingate/connector:latest"
    environment:
      - TWINGATE_NETWORK=mortigiamon
      - TWINGATE_ACCESS_TOKEN= # Add your access token here
      - TWINGATE_REFRESH_TOKEN= # Add your refresh token here
      - TWINGATE_LOG_ANALYTICS=v2
      - TWINGATE_LOG_LEVEL=3
    sysctls:
      net.ipv4.ping_group_range: "0 2147483647"